import json
import uuid
import asyncio

from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.core.serializers.json import DjangoJSONEncoder

from apps.discourse.models import (
    ChatMessage,
    ChatThread,
)
from apps.discourse.serializers import (
    ChatMessageSerializer,
    ChatThreadSerializer,
)
from apps.discourse.cache import ChatCacheManager

User = get_user_model()


class UUIDEncoder(DjangoJSONEncoder):
    """
    JSON encoder that handles UUID objects by converting them to strings.
    """
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)


class ChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for individual chat threads.
    """
    async def connect(self):
        try:
            self.user = self.scope['user']
            self.thread_id = self.scope['url_route']['kwargs']['thread_id']
            self.room_group_name = f'thread_{self.thread_id}'

            if self.user.is_anonymous:
                await self.close(code=4001)
                return

            is_participant = await self.is_thread_participant()
            if not is_participant:
                await self.close(code=4003)
                return

            # Join room group
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )

            await self.accept()

            # Send last 50 messages to the newly connected client
            messages = await self.get_thread_messages()
            if messages:
                await self.send(text_data=json.dumps({
                    'type': 'history',
                    'messages': messages
                }, cls=UUIDEncoder))
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"WebSocket connection error: {str(e)}")
            await self.close(code=4000)

    async def disconnect(self, close_code):
        # Leave room group
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        """
        Receive message from WebSocket.
        """
        try:
            data = json.loads(text_data)
            message_type = data.get('type', 'message')

            if message_type == 'message':
                # Create a new message
                message = await self.create_message(data.get('message', ''))

                # Send message to room group
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'chat_message',
                        'message': message
                    }
                )
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }, cls=UUIDEncoder))

    async def chat_message(self, event):
        """
        Receive message from room group and send to WebSocket.
        """
        message = event['message']

        await self.send(text_data=json.dumps({
            'type': 'message',
            'message': message
        }, cls=UUIDEncoder))

    @database_sync_to_async
    def is_thread_participant(self):
        """
        Check if the user is a participant in this thread.
        """
        try:
            thread = ChatThread.objects.get(id=self.thread_id)
            return thread.participants.filter(id=self.user.id).exists() or not thread.is_private
        except ChatThread.DoesNotExist:
            return False

    @database_sync_to_async
    def create_message(self, body):
        """
        Create a new message in the database.
        """
        thread = ChatThread.objects.get(id=self.thread_id)
        message = ChatMessage.objects.create(
            thread=thread,
            author=self.user,
            body=body
        )
        # Cache the message
        return ChatCacheManager.cache_message(message)

    @database_sync_to_async
    def get_thread_messages(self):
        """
        Get the last 50 messages for this thread.
        """
        # Try to get from cache first
        cached_messages = ChatCacheManager.get_thread_messages(self.thread_id)
        if cached_messages is not None:
            return cached_messages

        # If not in cache, get from database
        messages = ChatMessage.objects.filter(thread_id=self.thread_id).order_by('-created')[:50]
        if messages:
            # Cache and return the messages
            return ChatCacheManager.cache_thread_messages(self.thread_id, messages)
        return []


class ChatListConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for receiving updates about chat threads.
    """
    async def connect(self):
        try:
            self.user = self.scope['user']
            import logging
            logger = logging.getLogger(__name__)

            # Log connection attempt
            logger.info(f"ChatListConsumer connection attempt - User: {self.user}, Anonymous: {self.user.is_anonymous}")
            logger.info(f"ChatListConsumer scope keys: {list(self.scope.keys())}")
            logger.info(f"ChatListConsumer path: {self.scope.get('path')}")
            logger.info(f"ChatListConsumer query_string: {self.scope.get('query_string', b'').decode()}")

            # Check if user is authenticated
            if self.user.is_anonymous:
                logger.warning("ChatListConsumer connection rejected - User not authenticated")
                await self.close(code=4001)
                return

            self.room_group_name = f'user_{self.user.id}_threads'
            logger.info(f"ChatListConsumer connection accepted for user {self.user.id}")

            # Join room group with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    await self.channel_layer.group_add(
                        self.room_group_name,
                        self.channel_name
                    )
                    break
                except Exception as group_error:
                    logger.warning(f"Group add attempt {attempt + 1} failed: {group_error}")
                    if attempt == max_retries - 1:
                        raise group_error
                    await asyncio.sleep(1)  # Wait before retry

            await self.accept()

            # Send current threads to the newly connected client
            threads = await self.get_user_threads()
            await self.send(text_data=json.dumps({
                'type': 'thread_list',
                'threads': threads
            }, cls=UUIDEncoder))

            logger.info(f"WebSocket connection fully established for user {self.user.id}")
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"WebSocket connection error in ChatListConsumer: {str(e)}")
            import traceback
            logger.error(f"WebSocket error traceback: {traceback.format_exc()}")
            await self.close(code=4000)

    async def disconnect(self, close_code):
        # Leave room group
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def thread_update(self, event):
        """
        Receive thread update from room group and send to WebSocket.
        """
        thread = event['thread']

        # Send thread update to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'thread_update',
            'thread': thread
        }, cls=UUIDEncoder))

    @database_sync_to_async
    def get_user_threads(self):
        """
        Get all threads for this user.
        """
        threads = ChatThread.objects.filter(participants=self.user)
        return ChatThreadSerializer(threads, many=True).data


class TestWebSocketConsumer(AsyncWebsocketConsumer):
    """
    Simple WebSocket consumer for testing connectivity without authentication
    """
    async def connect(self):
        """Accept all connections for testing"""
        await self.accept()

        from django.utils import timezone

        # Send welcome message
        await self.send(text_data=json.dumps({
            'type': 'welcome',
            'message': 'WebSocket connection established successfully!',
            'timestamp': str(timezone.now()),
            'instructions': {
                'send_message': 'Send any JSON message to test echo functionality',
                'ping': 'Send {"type": "ping"} to test connection',
                'info': 'Send {"type": "info"} to get connection information'
            }
        }, cls=UUIDEncoder))

    async def disconnect(self, close_code):
        """Handle disconnect"""
        from django.utils import timezone
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"TestWebSocket disconnected with code: {close_code} at {timezone.now()}")

    async def receive(self, text_data):
        """Echo received messages back"""
        try:
            data = json.loads(text_data)
            from django.utils import timezone

            message_type = data.get('type', 'message')

            if message_type == 'ping':
                # Respond to ping with pong
                response = {
                    'type': 'pong',
                    'timestamp': str(timezone.now()),
                    'message': 'Connection is alive'
                }
            elif message_type == 'info':
                # Provide connection information
                response = {
                    'type': 'connection_info',
                    'channel_name': self.channel_name,
                    'scope_type': self.scope.get('type'),
                    'path': self.scope.get('path'),
                    'timestamp': str(timezone.now())
                }
            else:
                # Echo the message back with additional info
                response = {
                    'type': 'echo',
                    'original_message': data,
                    'echo_timestamp': str(timezone.now()),
                    'connection_info': {
                        'channel_name': self.channel_name,
                        'scope_type': self.scope.get('type'),
                        'path': self.scope.get('path'),
                    }
                }

            await self.send(text_data=json.dumps(response, cls=UUIDEncoder))

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format',
                'received': text_data
            }, cls=UUIDEncoder))
        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Error processing message: {str(e)}'
            }, cls=UUIDEncoder))


class AuthTestWebSocketConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for testing authentication
    """
    async def connect(self):
        """Test authentication and provide detailed info"""
        user = self.scope.get('user')
        query_string = self.scope.get('query_string', b'').decode()

        from django.utils import timezone

        # Always accept connection but provide auth info
        await self.accept()

        auth_info = {
            'type': 'auth_info',
            'user_authenticated': not user.is_anonymous if user else False,
            'user_id': getattr(user, 'id', None) if user and not user.is_anonymous else None,
            'user_username': getattr(user, 'username', None) if user and not user.is_anonymous else None,
            'query_string': query_string,
            'has_token': 'token=' in query_string,
            'scope_keys': list(self.scope.keys()),
            'timestamp': str(timezone.now())
        }

        await self.send(text_data=json.dumps(auth_info, cls=UUIDEncoder))

    async def disconnect(self, close_code):
        """Handle disconnect"""
        pass

    async def receive(self, text_data):
        """Provide user info on request"""
        try:
            data = json.loads(text_data)
            from django.utils import timezone

            if data.get('action') == 'get_user_info':
                user = self.scope.get('user')

                user_info = {
                    'type': 'user_info',
                    'user_authenticated': not user.is_anonymous if user else False,
                    'user_data': {
                        'id': getattr(user, 'id', None),
                        'username': getattr(user, 'username', None),
                        'email': getattr(user, 'email', None),
                        'is_staff': getattr(user, 'is_staff', None),
                        'is_active': getattr(user, 'is_active', None),
                    } if user and not user.is_anonymous else None,
                    'timestamp': str(timezone.now())
                }

                await self.send(text_data=json.dumps(user_info, cls=UUIDEncoder))
            else:
                await self.send(text_data=json.dumps({
                    'type': 'echo',
                    'message': 'Send {"action": "get_user_info"} to get user information',
                    'received': data
                }, cls=UUIDEncoder))

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }, cls=UUIDEncoder))


class DebugWebSocketConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for debugging connection issues
    """
    async def connect(self):
        """Accept all connections and log detailed info"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # Log all scope information
            logger.info(f"Debug WebSocket connection attempt:")
            logger.info(f"  Path: {self.scope.get('path')}")
            logger.info(f"  Query string: {self.scope.get('query_string', b'').decode()}")
            logger.info(f"  Headers: {dict(self.scope.get('headers', []))}")
            logger.info(f"  User: {self.scope.get('user')}")
            logger.info(f"  User authenticated: {not self.scope.get('user', AnonymousUser()).is_anonymous}")

            # Always accept the connection
            await self.accept()

            # Send debug info
            from django.utils import timezone
            debug_info = {
                'type': 'debug_info',
                'connection_successful': True,
                'path': self.scope.get('path'),
                'query_string': self.scope.get('query_string', b'').decode(),
                'user_authenticated': not self.scope.get('user', AnonymousUser()).is_anonymous,
                'user_id': getattr(self.scope.get('user'), 'id', None),
                'timestamp': str(timezone.now()),
                'message': 'Debug WebSocket connection established successfully!'
            }

            await self.send(text_data=json.dumps(debug_info, cls=UUIDEncoder))
            logger.info("Debug WebSocket connection established successfully")

        except Exception as e:
            logger.error(f"Debug WebSocket connection error: {str(e)}")
            import traceback
            logger.error(f"Debug WebSocket error traceback: {traceback.format_exc()}")
            await self.close(code=4000)

    async def disconnect(self, close_code):
        """Handle disconnect"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Debug WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        """Echo back any received data"""
        try:
            data = json.loads(text_data)
            from django.utils import timezone

            response = {
                'type': 'debug_echo',
                'received': data,
                'timestamp': str(timezone.now()),
                'user_authenticated': not self.scope.get('user', AnonymousUser()).is_anonymous,
                'message': 'Message received and echoed back'
            }

            await self.send(text_data=json.dumps(response, cls=UUIDEncoder))

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format',
                'received_raw': text_data
            }, cls=UUIDEncoder))