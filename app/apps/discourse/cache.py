from django.core.cache import cache

from config import settings



class ChatCacheManager:
    """
    Manages caching for chat messages using Redis.
    """

    @staticmethod
    def get_thread_messages_key(thread_id):
        """Get the Redis key for a thread's messages."""
        return f'thread:{thread_id}:messages'

    @staticmethod
    def cache_message(message):
        """
        Cache a new message in the thread's message list.
        """
        thread_id = str(message.thread.id)
        cache_key = ChatCacheManager.get_thread_messages_key(thread_id)

        cached_messages = cache.get(cache_key) or []

        from apps.discourse.serializers import ChatMessageSerializer
        serialized = ChatMessageSerializer(message).data
        cached_messages.append(serialized)

        # Keep only the last 50 messages
        if len(cached_messages) > 50:
            cached_messages = cached_messages[-50:]

        # Update cache with timeout from settings
        cache.set(cache_key, cached_messages, timeout=settings.MINIMUM_CACHE_TTL)

        return serialized

    @staticmethod
    def get_thread_messages(thread_id, limit=30):
        """
        Get cached messages for a thread.
        Returns None if not in cache or if cache fails.

        Args:
            thread_id: The thread ID
            limit: Maximum number of messages to return

        Returns:
            List of serialized messages or None if not cached/cache fails
        """
        try:
            cache_key = ChatCacheManager.get_thread_messages_key(thread_id)
            cached_messages = cache.get(cache_key)

            if cached_messages is not None:
                # Return only the requested number of messages
                # Messages are stored newest first, so we take the first 'limit' messages
                return cached_messages[:limit]

            return cached_messages
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache get error in get_thread_messages: {e}")
            return None

    @staticmethod
    def cache_thread_messages(thread_id, messages):
        """
        Cache a list of messages for a thread.

        Args:
            thread_id: The thread ID
            messages: List of message objects or serialized messages

        Returns:
            Serialized messages
        """
        cache_key = ChatCacheManager.get_thread_messages_key(thread_id)

        # Serialize messages if they're not already serialized
        if messages and not isinstance(messages[0], dict):
            from apps.discourse.serializers import ChatMessageSerializer
            serialized = ChatMessageSerializer(messages, many=True).data
        else:
            serialized = messages

        # We store up to 50 messages in cache (more than the default page size)
        # This allows for some flexibility in page size without cache misses
        if len(serialized) > 50:
            serialized = serialized[:50]

        cache.set(cache_key, serialized, timeout=settings.MINIMUM_CACHE_TTL)

        return serialized

    @staticmethod
    def invalidate_thread_cache(thread_id):
        """
        Remove a thread's messages from cache.
        """
        cache_key = ChatCacheManager.get_thread_messages_key(thread_id)
        cache.delete(cache_key)
