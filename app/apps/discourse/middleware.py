from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.exceptions import (
    InvalidToken,
    TokenError,
)

from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.db import close_old_connections

User = get_user_model()



@database_sync_to_async
def get_user_from_token(token_key):
    import logging
    logger = logging.getLogger(__name__)

    try:
        access_token = AccessToken(token_key)
        user_id = access_token.payload.get('user_id')
        user = User.objects.get(id=user_id)
        logger.info(f"WebSocket token authentication successful for user {user_id}")
        return user
    except (InvalidToken, TokenError) as e:
        logger.warning(f"WebSocket token validation failed: {e}")
        return AnonymousUser()
    except User.DoesNotExist:
        logger.warning(f"WebSocket token valid but user {user_id} not found")
        return AnonymousUser()

class TokenAuthMiddleware(BaseMiddleware):
    """
    Custom middleware that takes a token from the query string and authenticates via JWT.
    """
    async def __call__(self, scope, receive, send):
        import logging
        logger = logging.getLogger(__name__)

        # Close old database connections to prevent usage of timed out connections
        close_old_connections()

        # Get the token from query string
        query_string = scope['query_string'].decode()
        query_params = dict(x.split('=') for x in query_string.split('&') if x)
        token = query_params.get('token', None)

        path = scope.get('path', 'unknown')
        logger.info(f"WebSocket connection attempt to {path} with token: {'present' if token else 'missing'}")

        if token:
            scope['user'] = await get_user_from_token(token)
        else:
            logger.info(f"No token provided for WebSocket connection to {path}")
            scope['user'] = AnonymousUser()

        return await super().__call__(scope, receive, send)

def TokenAuthMiddlewareStack(inner):
    return TokenAuthMiddleware(inner)
