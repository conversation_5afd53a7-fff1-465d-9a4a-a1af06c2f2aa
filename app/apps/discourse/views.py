from rest_framework import (
    filters,
    permissions,
    status,
    viewsets,
)
from rest_framework.response import Response
from rest_framework.decorators import action, api_view
import asyncio

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db.models import Prefetch, Q
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.utils import timezone

from apps.discourse.models import (
    ChatMessage,
    ChatThread,
    ThreadParticipant,
)
from apps.discourse.serializers import (
    ChatMessageSerializer,
    ChatThreadSerializer,
)
from apps.discourse.permissions import (
    IsMessageThreadParticipant,
    IsThreadParticipant,
)
from apps.discourse.cache import ChatCacheManager


User = get_user_model()


class ChatThreadViewSet(viewsets.ModelViewSet):
    """
    API endpoint for chat threads.
    """
    serializer_class = ChatThreadSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        user = self.request.user
        queryset = ChatThread.objects.filter(participants=user)

        # Add public threads if requested
        include_public = self.request.query_params.get(
            'include_public', 'false').lower() == 'true'
        if include_public:
            queryset = queryset.union(
                ChatThread.objects.filter(is_private=False)
            )

        queryset = queryset.prefetch_related(
            'participants',
            Prefetch(
                'messages',
                queryset=ChatMessage.objects.order_by('-created')[:1],
                to_attr='last_messages'
            )
        ).order_by('-created')

        return queryset

    @action(detail=True, methods=['get'])
    def messages(self, request, id=None):
        """
        Get messages for a specific thread with pagination.
        """
        thread = self.get_object()

        # Get pagination parameters
        limit = int(request.query_params.get('limit', 30))
        offset = int(request.query_params.get('offset', 0))

        # Try to get from cache for first page
        if offset == 0:
            cached_messages = ChatCacheManager.get_thread_messages(thread.id, limit)
            if cached_messages is not None:
                total_count = ChatMessage.objects.filter(thread=thread).count()
                return Response({
                    'results': cached_messages,
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                })

        # Get messages from database with pagination
        messages = ChatMessage.objects.filter(thread=thread).order_by('-created')[offset:offset+limit]
        serializer = ChatMessageSerializer(messages, many=True)

        if offset == 0 and messages:
            ChatCacheManager.cache_thread_messages(thread.id, messages)

        total_count = ChatMessage.objects.filter(thread=thread).count()
        return Response({
            'results': serializer.data,
            'total': total_count,
            'limit': limit,
            'offset': offset,
        })

    def get_permissions(self):
        if self.action in ['retrieve', 'update', 'partial_update', 'destroy']:
            return [permissions.IsAuthenticated(), IsThreadParticipant()]
        return super().get_permissions()

    @action(detail=True, methods=['post'])
    def add_participant(self, request, id=None):
        thread = self.get_object()
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)
            if user in thread.participants.all():
                return Response(
                    {'detail': 'User is already a participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            ThreadParticipant.objects.create(thread=thread, user=user)
            return Response(
                {'detail': 'User added successfully.'},
                status=status.HTTP_200_OK
            )
        except User.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def remove_participant(self, request, id=None):
        thread = self.get_object()
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)
            if user == request.user and thread.participants.count() <= 1:
                return Response(
                    {'detail': 'Cannot remove yourself as the only participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            participant = ThreadParticipant.objects.filter(thread=thread, user=user)
            if participant.exists():
                participant.delete()
                return Response(
                    {'detail': 'User removed successfully.'},
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    {'detail': 'User is not a participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except User.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )


class ChatMessageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for chat messages.
    """
    serializer_class = ChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated, IsMessageThreadParticipant]

    def get_queryset(self):
        queryset = ChatMessage.objects.select_related('author', 'thread')

        # Filter by thread if provided
        thread_id = self.request.query_params.get('thread')
        if thread_id:
            limit = int(self.request.query_params.get('limit', 30))
            offset = int(self.request.query_params.get('offset', 0))

            if offset == 0:
                cached_messages = ChatCacheManager.get_thread_messages(thread_id, limit)
                if cached_messages is not None:
                    return ChatMessage.objects.none()

            queryset = queryset.filter(thread_id=thread_id).order_by('-created')

            if offset == 0:
                messages = queryset[:limit]
                if messages:
                    ChatCacheManager.cache_thread_messages(thread_id, messages)

        return queryset

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        message = ChatMessage.objects.get(id=serializer.data['id'])
        ChatCacheManager.cache_message(message)

        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def list(self, request, *args, **kwargs):
        thread_id = request.query_params.get('thread')
        if thread_id:
            limit = int(request.query_params.get('limit', 30))
            offset = int(request.query_params.get('offset', 0))

            # Only use cache for the first page
            if offset == 0:
                # Try to get cached messages
                cached_messages = ChatCacheManager.get_thread_messages(thread_id, limit)
                if cached_messages is not None:
                    # Calculate total count for pagination info
                    total_count = ChatMessage.objects.filter(thread_id=thread_id).count()

                    # Create a paginated response
                    return Response({
                        'results': cached_messages,
                        'total': total_count,
                        'limit': limit,
                        'offset': offset,
                    })

            # Get messages from database with pagination
            queryset = ChatMessage.objects.filter(thread_id=thread_id).order_by('-created')[offset:offset+limit]
            serializer = self.get_serializer(queryset, many=True)

            if offset == 0 and queryset:
                ChatCacheManager.cache_thread_messages(thread_id, queryset)

            total_count = ChatMessage.objects.filter(thread_id=thread_id).count()
            return Response({
                'results': serializer.data,
                'total': total_count,
                'limit': limit,
                'offset': offset,
            })

        # Fall back to database if not cached
        return super().list(request, *args, **kwargs)


@api_view(['GET'])
def websocket_health(request):
    """
    WebSocket health check endpoint
    """
    try:
        from channels.layers import get_channel_layer
        from django.conf import settings
        import logging

        logger = logging.getLogger(__name__)
        checks = {}

        # Check channel layer configuration
        try:
            channel_layer = get_channel_layer()
            if channel_layer:
                checks['channel_layer'] = {
                    'healthy': True,
                    'message': f"Channel layer configured: {channel_layer.__class__.__name__}"
                }

                # Check if it's InMemory or Redis (without testing connection)
                if 'InMemory' in channel_layer.__class__.__name__:
                    checks['redis_connection'] = {
                        'healthy': True,
                        'message': 'Using InMemoryChannelLayer (Redis not required)'
                    }
                else:
                    checks['redis_connection'] = {
                        'healthy': True,
                        'message': f'Redis channel layer configured (connection not tested during health check)'
                    }
            else:
                checks['channel_layer'] = {
                    'healthy': False,
                    'message': "Channel layer not configured"
                }
        except Exception as e:
            checks['channel_layer'] = {
                'healthy': False,
                'message': f"Channel layer error: {str(e)}"
            }

        # Check Redis URL configuration
        redis_url = getattr(settings, 'REDIS_CHANNELS_URL', None)
        if redis_url:
            checks['redis_config'] = {
                'healthy': True,
                'message': f"Redis URL configured: {redis_url[:20]}..."
            }
        else:
            checks['redis_config'] = {
                'healthy': False,
                'message': "REDIS_CHANNELS_URL not configured"
            }

        # Check ASGI application
        asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
        if asgi_app:
            checks['asgi_config'] = {
                'healthy': True,
                'message': f"ASGI application configured: {asgi_app}"
            }
        else:
            checks['asgi_config'] = {
                'healthy': False,
                'message': "ASGI_APPLICATION not configured"
            }

        # Check channel layers settings
        channel_layers_config = getattr(settings, 'CHANNEL_LAYERS', {})
        if channel_layers_config:
            checks['channel_layers_config'] = {
                'healthy': True,
                'message': f"Channel layers configured with backend: {channel_layers_config.get('default', {}).get('BACKEND', 'Unknown')}"
            }
        else:
            checks['channel_layers_config'] = {
                'healthy': False,
                'message': "CHANNEL_LAYERS not configured"
            }

        overall_healthy = all(check['healthy'] for check in checks.values())

        health_result = {
            'healthy': overall_healthy,
            'checks': checks,
            'timestamp': str(timezone.now()) if 'timezone' in globals() else 'unknown'
        }

        status_code = status.HTTP_200_OK if health_result['healthy'] else status.HTTP_503_SERVICE_UNAVAILABLE

        return Response(health_result, status=status_code)

    except Exception as e:
        import traceback
        return Response({
            'healthy': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


async def test_channel_layer(channel_layer):
    """
    Test channel layer connectivity
    """
    try:
        # Test basic send/receive
        test_channel = "health_check_test"
        test_message = {"type": "test.message", "text": "health_check"}

        await channel_layer.send(test_channel, test_message)
        received = await channel_layer.receive(test_channel)

        if received == test_message:
            return {
                'healthy': True,
                'message': 'Redis connection test successful'
            }
        else:
            return {
                'healthy': False,
                'message': 'Redis connection test failed - message mismatch'
            }
    except Exception as e:
        return {
            'healthy': False,
            'message': f'Redis connection test failed: {str(e)}'
        }


@api_view(['GET'])
def basic_health_check(request):
    """
    Basic health check endpoint that doesn't depend on external services
    """
    try:
        from django.conf import settings
        import django

        health_info = {
            'status': 'healthy',
            'django_version': django.get_version(),
            'debug': settings.DEBUG,
            'allowed_hosts': settings.ALLOWED_HOSTS,
            'installed_apps_count': len(settings.INSTALLED_APPS),
            'channels_installed': 'channels' in settings.INSTALLED_APPS,
            'asgi_application': getattr(settings, 'ASGI_APPLICATION', None),
        }

        return Response(health_info, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'status': 'unhealthy',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def simple_websocket_health(request):
    """
    Simple WebSocket health check that doesn't test Redis connectivity
    """
    try:
        from django.conf import settings

        checks = {}

        # Check if channels is installed
        channels_installed = 'channels' in settings.INSTALLED_APPS
        checks['channels_installed'] = {
            'healthy': channels_installed,
            'message': f"Channels app installed: {channels_installed}"
        }

        # Check ASGI application
        asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
        checks['asgi_config'] = {
            'healthy': bool(asgi_app),
            'message': f"ASGI application: {asgi_app or 'Not configured'}"
        }

        # Check Redis URL configuration
        redis_url = getattr(settings, 'REDIS_CHANNELS_URL', None)
        checks['redis_config'] = {
            'healthy': bool(redis_url),
            'message': f"Redis URL configured: {bool(redis_url)}"
        }

        # Check channel layers configuration
        channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
        checks['channel_layers'] = {
            'healthy': bool(channel_layers),
            'message': f"Channel layers configured: {bool(channel_layers)}"
        }

        # Try to import channel layer (without testing connection)
        try:
            from channels.layers import get_channel_layer
            channel_layer = get_channel_layer()
            checks['channel_layer_import'] = {
                'healthy': channel_layer is not None,
                'message': f"Channel layer import: {'Success' if channel_layer else 'Failed'}"
            }
        except Exception as e:
            checks['channel_layer_import'] = {
                'healthy': False,
                'message': f"Channel layer import failed: {str(e)}"
            }

        overall_healthy = all(check['healthy'] for check in checks.values())

        result = {
            'healthy': overall_healthy,
            'checks': checks,
            'timestamp': str(timezone.now())
        }

        status_code = status.HTTP_200_OK if overall_healthy else status.HTTP_503_SERVICE_UNAVAILABLE
        return Response(result, status=status_code)

    except Exception as e:
        import traceback
        return Response({
            'healthy': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def generate_test_token(request):
    """
    Generate a test JWT token for WebSocket testing
    """
    try:
        from rest_framework_simplejwt.tokens import AccessToken
        from django.contrib.auth import get_user_model

        User = get_user_model()

        # Get user by email
        identifier = request.data.get('email')
        if not identifier:
            return Response({
                'error': 'Please provide email'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Try to find user by email
            user = User.objects.get(email=identifier)
        except User.DoesNotExist:
            return Response({
                'error': f'User not found: {identifier}'
            }, status=status.HTTP_404_NOT_FOUND)

        # Generate token
        token = AccessToken.for_user(user)

        return Response({
            'token': str(token),
            'user_id': user.id,
            'email': user.email,
            'websocket_urls': {
                'chat_list': f'wss://api.dev.advaya.life/ws/chat/?token={token}',
                'user_channel': f'wss://api.dev.advaya.life/ws/user/?token={token}',
                'test_endpoint': f'wss://api.dev.advaya.life/ws/test/?token={token}',
                'auth_test': f'wss://api.dev.advaya.life/ws/auth-test/?token={token}',
                'debug_endpoint': f'wss://api.dev.advaya.life/ws/debug/?token={token}'
            },
            'note': 'Use WSS (secure WebSocket) for all connections. WS (non-secure) will be rejected.',
            'instructions': {
                'postman': 'Use the websocket_urls above in Postman WebSocket requests',
                'curl': f'curl -H "Authorization: Bearer {token}" https://api.dev.advaya.life/api/health/'
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        import traceback
        return Response({
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def redis_health_check(request):
    """
    Dedicated Redis health check endpoint
    """
    try:
        from django.conf import settings
        import redis
        import ssl
        import urllib.parse

        redis_url = getattr(settings, 'REDIS_CHANNELS_URL', None)
        if not redis_url:
            return Response({
                'healthy': False,
                'error': 'REDIS_CHANNELS_URL not configured'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

        checks = {}

        # Test direct Redis connection
        try:
            if redis_url.startswith('rediss://'):
                # SSL connection
                parsed = urllib.parse.urlparse(redis_url)
                redis_client = redis.Redis(
                    host=parsed.hostname,
                    port=parsed.port or 6379,
                    password=parsed.password,
                    ssl=True,
                    ssl_cert_reqs=ssl.CERT_NONE,
                    ssl_check_hostname=False,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
            else:
                # Non-SSL connection
                redis_client = redis.from_url(
                    redis_url,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )

            # Test ping
            redis_client.ping()
            checks['redis_ping'] = {
                'healthy': True,
                'message': 'Redis PING successful'
            }

            # Test set/get
            test_key = 'health_check_test'
            test_value = 'test_value'
            redis_client.set(test_key, test_value, ex=30)
            retrieved = redis_client.get(test_key)

            if retrieved and retrieved.decode() == test_value:
                checks['redis_operations'] = {
                    'healthy': True,
                    'message': 'Redis SET/GET successful'
                }
                redis_client.delete(test_key)
            else:
                checks['redis_operations'] = {
                    'healthy': False,
                    'message': 'Redis SET/GET failed'
                }

        except Exception as redis_error:
            checks['redis_connection'] = {
                'healthy': False,
                'message': f'Redis connection failed: {str(redis_error)}'
            }

        # Test channel layer
        try:
            from channels.layers import get_channel_layer
            channel_layer = get_channel_layer()

            if channel_layer:
                checks['channel_layer'] = {
                    'healthy': True,
                    'message': f'Channel layer: {channel_layer.__class__.__name__}'
                }
            else:
                checks['channel_layer'] = {
                    'healthy': False,
                    'message': 'Channel layer not configured'
                }
        except Exception as channel_error:
            checks['channel_layer'] = {
                'healthy': False,
                'message': f'Channel layer error: {str(channel_error)}'
            }

        overall_healthy = all(check['healthy'] for check in checks.values())

        result = {
            'healthy': overall_healthy,
            'redis_url_type': 'SSL' if redis_url.startswith('rediss://') else 'Non-SSL',
            'checks': checks,
            'timestamp': str(timezone.now())
        }

        status_code = status.HTTP_200_OK if overall_healthy else status.HTTP_503_SERVICE_UNAVAILABLE
        return Response(result, status=status_code)

    except Exception as e:
        import traceback
        return Response({
            'healthy': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def websocket_debug_info(request):
    """
    Debug information for WebSocket configuration
    """
    import os
    from django.conf import settings
    from apps.discourse.routing import websocket_urlpatterns

    return Response({
        'debug_mode': settings.DEBUG,
        'allowed_hosts': settings.ALLOWED_HOSTS,
        'channel_layers': settings.CHANNEL_LAYERS,
        'redis_channels_url': settings.REDIS_CHANNELS_URL,
        'use_redis_channels': os.environ.get('USE_REDIS_CHANNELS', 'not_set'),
        'use_redis_channels_parsed': settings.USE_REDIS_CHANNELS if hasattr(settings, 'USE_REDIS_CHANNELS') else 'not_available',
        'asgi_application': settings.ASGI_APPLICATION,
        'websocket_routing': {
            'total_patterns': len(websocket_urlpatterns),
            'patterns': [str(p.pattern) for p in websocket_urlpatterns]
        },
        'environment_info': {
            'django_env': os.environ.get('DJANGO_ENV', 'not_set'),
            'django_settings_module': os.environ.get('DJANGO_SETTINGS_MODULE', 'not_set')
        },
        'timestamp': timezone.now().isoformat()
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
def check_user_exists(request):
    """
    Check if a user exists by ID
    """
    user_id = request.query_params.get('user_id')
    if not user_id:
        return Response({
            'error': 'user_id parameter is required'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.get(id=user_id)
        return Response({
            'exists': True,
            'user_id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name
        }, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({
            'exists': False,
            'user_id': user_id
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
