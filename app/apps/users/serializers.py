from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from django.contrib.auth import (
    get_user_model,
    password_validation as validators,
)
from django.db.models import Count

from config import settings

from apps.users.models import (
    Profile,
    SYNCED_USER_ATTRIBUTES,
)

from utils.cache.content import get_cached_product_data
from utils.chargebee.users import (
    update_user_details as chargebee_update_user_details,
)
from utils.hubspot.users import (
    create_contact as hubspot_create_contact,
    is_user_in_mailing_list as hubspot_is_user_in_mailing_list,
    subscribe_user,
    subscribe_new_user,
    update_user_details as hubspot_update_user_details,
    update_user_interests_from_mailing_lists,
)
from utils.nodebb.auth import (
    register_user as nodebb_register_user,
    update_user_profile_details as nodebb_update_user_details,
    add_user_email as nodebb_add_user_email,
    update_account_verified as nodebb_update_account_verified,
)



User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(format='hex_verbose', read_only=True)
    password = serializers.CharField(style={'input_type': 'password'}, write_only=True)
    has_billing = serializers.SerializerMethodField()

    class Meta:
        model = User
        # extra_kwargs = {'password': {'write_only': True}}
        fields = ('id',
                  'email',
                  'password',
                  'first_name',
                  'last_name',
                  'is_verified',
                  'plan_id',
                  'has_billing',)
                  # 'chargebee_id',
                  # 'is_staff',
                  # 'is_admin',
                  # 'last_login',
                  # 'created')

    def get_has_billing(self, instance):
        return bool(instance.chargebee_id)

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'email': instance.email,
            'first_name': instance.first_name,
            'last_name': instance.last_name,
            'emailVerified': instance.is_verified,
            'plan_id': instance.plan_id,
            'hasBilling': self.get_has_billing(instance),
        }

    def validate_email(self, value):
        # force lowercase
        return value.lower()

    def validate_password(self, value):
        try:
            validators.validate_password(value, self.instance)
        except ValidationError as exc:
            raise serializers.ValidationError({'detail': str(exc)})

        return value

    def create(self, validated_data):
        user = super().create(validated_data)
        user.set_password(validated_data['password'])
        user.save()
        avatar_generator_url = settings.AVATAR_GENERATOR_URL
        user_params = f"{user.first_name}+{user.last_name}".replace(' ', '')
        pic_url = f"{avatar_generator_url}{user_params}"
        user.profile.map_details({
            'nickname': user.first_name.lower(),
            'profile_photo': f"{pic_url}&background=ffe5e3&size=180",
            'profile_photo_tiny': f"{pic_url}&background=ffe5e3&size=48",
        })

        # triggers account activation workflow
        hubspot_create_contact(user)
        subscribe_new_user(user)
        if not user.subscribed_to_newsletter:
            user.subscribed_to_newsletter = hubspot_is_user_in_mailing_list(user)
            user.save()
        user.trigger_account_activation_flow()
        update_user_interests_from_mailing_lists(user)
        nodebb_register_user(user)
        nodebb_add_user_email(user)
        nodebb_update_account_verified(user)

        return user

    def update(self, instance, validated_data):
        # email_to_update = None
        # if validated_data.get('email', False):
        #     email_to_update = instance.email

        previous_email = instance.email if 'email' in validated_data else False
        user = super().update(instance, validated_data)
        if 'password' in validated_data:
            user.set_password(validated_data['password'])
            user.save()

        hubspot_update_user_details(user, previous_email=previous_email)
        chargebee_update_user_details(user)
        nodebb_update_user_details(user)

        if 'email' in validated_data:
            user.trigger_account_activation_flow(
                "email_update_confirmation_url")
            nodebb_add_user_email(user)

        # if email_to_update is not None:
        #     hubspot_change_user_email(user, email_to_update)
        #     chargebee_update_user_details(user)

        # any_sync_attrs_to_update = any([validated_data.get(attr, False) for
        #                                 attr in SYNCED_USER_ATTRIBUTES])
        # if any_sync_attrs_to_update:
        #     hubspot_update_user_details(user)
        #     chargebee_update_user_details(user)

        return user


class ChangePasswordSerializer(serializers.Serializer):
    model = User
    """
    Serializer for password change endpoint.
    """
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)


class ResetPasswordSerializer(serializers.Serializer):
    model = User
    """
    Serializer for password change endpoint.
    """
    token = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)


class PublicProfileSerializer(serializers.ModelSerializer):
    is_following = serializers.SerializerMethodField()
    following_back = serializers.SerializerMethodField()
    chat_thread_id = serializers.SerializerMethodField()

    class Meta:
        model = Profile
        fields = ('public_details',
                  'social_networks',
                  'interests',
                  'is_following',
                  'following_back',
                  'chat_thread_id')

    def get_is_following(self, instance):
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            return request.user.profile.is_following(instance.user)
        return False

    def get_following_back(self, instance):
        """
        Check if the profile user follows back the current authenticated user.
        Returns True if the profile user follows the current user, False otherwise.
        """
        request = self.context.get('request')
        if request and hasattr(request, 'user') and not request.user.is_anonymous:
            # Check if this profile's user follows the current authenticated user
            return instance.is_following(request.user)
        return False

    def get_chat_thread_id(self, instance):
        """
        Return the thread ID if a private chat thread exists between
        the current user and this profile's user.

        Note: this makes caching difficult, so it's a compromise at the moment.
        """
        request = self.context.get('request')
        if not request or not hasattr(request, 'user') or request.user.is_anonymous:
            return None

        # Importing here to avoid circular imports
        from apps.discourse.models import ChatThread

        # Find private threads where both users are participants
        threads = ChatThread.objects.filter(
            is_private=True,
            participants=request.user
        ).filter(
            participants=instance.user
        )

        # If exactly two participants (just these two users)
        thread = threads.annotate(
            participant_count=Count('participants')
        ).filter(participant_count=2).first()

        return str(thread.id) if thread else None

    def to_representation(self, instance):
        user_profile_data = {
            'id': instance.user.id,
            'first_name': instance.public_details.get('first_name', ''),
            'last_name': instance.public_details.get('last_name', ''),
            'location': instance.public_details.get('location', '').strip(),
            'nickname': instance.public_details.get('nickname', ''),
            'picture': instance.public_details.get('profile_photo', ''),
            'pictureTiny': instance.public_details.get('profile_photo_tiny', ''),
            'biography': instance.public_details.get('biography', ''),
            'interests': [interest.get('title') for interest in
                          instance.interests.values()],
            'clubsCount': instance.public_details.get('clubs_count', 0),
            'coursesCount': instance.public_details.get('courses_count', 0),
            'connectionsCount': instance.public_details.get('connections_count', 0),
            'memberSince': instance.public_details.get('member_since', ''),
            'socialNetworks': instance.social_networks,
            'isFollowing': self.get_is_following(instance),
            'followingBack': self.get_following_back(instance),
            'chatThreadId': self.get_chat_thread_id(instance),
            'privacy': "private",
        }

        content_rules = {
            'courses': instance.privacy_rules.get('courses', 'private'),
            'clubs': instance.privacy_rules.get('clubs', 'private'),
            'webinars': instance.privacy_rules.get('webinars', 'private'),
            'audio-journeys': instance.privacy_rules.get('audio-journeys', 'private'),
        }

        if any([rule == 'public' for rule in content_rules.values()]):
            user_profile_data['privacy'] = 'public'
            user_content = instance.user.content.select_related("enrolment").all()

            user_profile_data['courses'] = {
                course.slug: get_cached_product_data(
                    course, include_user_content=False)
                for course in user_content.filter(content_type='courses') if
                course != {}}
            user_profile_data['clubs'] = {
                club.slug: get_cached_product_data(
                    club, include_user_content=False)
                for club in user_content.filter(content_type='clubs') if
                club != {}}
            user_profile_data['audio-journeys'] = {
                audio_journey.slug: get_cached_product_data(
                    audio_journey, include_user_content=False)
                for audio_journey in user_content.filter(content_type='audio-journeys') if
                audio_journey != {}}
            user_profile_data['webinars'] = {
                webinar.slug: get_cached_product_data(
                    webinar, include_user_content=False)
                for webinar in user_content.filter(content_type='webinars') if
                webinar != {}}

        return user_profile_data


class SimplePublicProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ('public_details'
                  'private_details',
                  'interests')

    def to_representation(self, instance):
        return {
            "id": instance.user.id,
            "first_name": instance.public_details.get('first_name', ''),
            "last_name": instance.public_details.get('last_name', ''),
            "location": instance.public_details.get('location', '').strip(),
            "nickname": instance.public_details.get('nickname', ''),
            "picture": instance.public_details.get('profile_photo', ''),
            "pictureTiny": instance.public_details.get('profile_photo_tiny', ''),
            'interests': [interest.get('title') for interest in
                          instance.interests.values()],
        }


class PrivateProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ('private_details',
                  'social_networks',
                  'interests')

    def to_representation(self, instance):
        return {
            'picture': instance.private_details.get('profile_photo', ''),
            'pictureTiny': instance.private_details.get('profile_photo_tiny', ''),
            'nickname': instance.private_details.get('nickname', ''),
            'location': instance.private_details.get('location', '').strip(),
            'biography': instance.private_details.get('biography', ''),
            'gender': instance.private_details.get('gender', ''),
            'birthdate': instance.private_details.get('birthdate', ''),
            'interests': [interest.get('title') for interest in
                          instance.interests.values()],
            'clubsCount': instance.public_details.get('clubs_count', 0),
            'coursesCount': instance.public_details.get('courses_count', 0),
            'connectionsCount': instance.public_details.get('connections_count', 0),
            'memberSince': instance.public_details.get('member_since', ''),
            'socialNetworks': instance.social_networks,
            'hasCompletedOnboarding': instance.onboarding_completed,
        }
