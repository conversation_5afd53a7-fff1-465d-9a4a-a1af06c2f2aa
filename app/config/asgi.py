"""
ASGI config for api project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/howto/deployment/asgi/
"""

import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Import Django first and set up the application
from django.core.asgi import get_asgi_application
django_asgi_app = get_asgi_application()

# Import the rest of the components
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.security.websocket import AllowedHostsOriginValidator

from apps.discourse.middleware import TokenAuthMiddlewareStack
from apps.discourse.routing import (
    websocket_urlpatterns as discourse_urlpatterns,
)
from apps.users.websocket_urls import (
    url_patterns as user_urlpatterns,
)

websocket_urlpatterns = user_urlpatterns + discourse_urlpatterns

# For debugging WebSocket issues, we can temporarily bypass AllowedHostsOriginValidator
# This should be re-enabled in production
application = ProtocolTypeRouter({
    "http": django_asgi_app,
    'websocket': TokenAuthMiddlewareStack(URLRouter(websocket_urlpatterns)),
    # 'websocket': AllowedHostsOriginValidator(
    #     TokenAuthMiddlewareStack(URLRouter(websocket_urlpatterns))
    # ),
})
