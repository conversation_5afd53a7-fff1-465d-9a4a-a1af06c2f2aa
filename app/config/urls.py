from rest_framework_simplejwt.views import TokenRefreshView

from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.auth import logout
from django.urls import include
from django.urls import path

from apps.authentication.views import RoleTokenObtainPairView
from apps.users.views.users import (
    ChangePasswordView,
    ResetPasswordView,
)
from apps.discourse.views import websocket_health, basic_health_check, simple_websocket_health, generate_test_token, redis_health_check, websocket_debug_info
from config import settings
from config.api import api



urlpatterns = [
    path('admin/', admin.site.urls),

    path('logout/', logout, {'next_page': '/'}, name='logout'),

    # API ROOT
    path('api/', include(api.urls)),

    # DRF API authentification
    path('api-auth/',
         include('rest_framework.urls', namespace='rest_framework')),

    # JWT
    path('api/auth/token/', RoleTokenObtainPairView.as_view(),
         name='token_obtain_pair'),
    path('api/auth/token/refresh/', TokenRefreshView.as_view(),
         name='token_refresh'),
    path('api/auth/change-password/', ChangePasswordView.as_view(),
         name='change-password'),
    path('api/auth/reset-password/', ResetPasswordView.as_view(),
         name='reset-password'),

    # Health checks
    path('api/health/', basic_health_check, name='basic-health'),
    path('api/websocket/health/', websocket_health, name='websocket-health'),
    path('api/websocket/simple-health/', simple_websocket_health, name='simple-websocket-health'),
    path('api/redis/health/', redis_health_check, name='redis-health'),

    # Testing utilities
    path('api/generate-test-token/', generate_test_token, name='generate-test-token'),
    path('api/websocket/debug/', websocket_debug_info, name='websocket-debug'),
]

urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

if settings.DEBUG:
    urlpatterns = [
        path('__debug__/', include('debug_toolbar.urls')),
    ] + urlpatterns
