alerts:
- rule: DEPLOYMENT_FAILED
- rule: DEPLOYMENT_LIVE
- rule: DOMAIN_FAILED
databases:
- cluster_name: advaya-api-dev-db
  db_name: defaultdb
  db_user: doadmin
  engine: PG
  name: advaya-api-dev-db
  production: true
  version: "15"
- cluster_name: advaya-redis-next
  engine: REDIS
  name: advaya-redis-next
  production: true
  version: "7"
domains:
- domain: api.dev.advaya.life
  type: PRIMARY
envs:
- key: DEBUG
  scope: RUN_AND_BUILD_TIME
  value: "True"
- key: SECRET_KEY
  scope: RUN_AND_BUILD_TIME
  value: django-insecure-7po!+rn
- key: ALLOWED_HOSTS
  scope: RUN_AND_BUILD_TIME
  value: '["*", "advaya.life", "api.dev.advaya.life", "dev.advaya.life", "127.0.0.1",
    "localhost"]'
- key: ENABLE_SENTRY
  scope: RUN_AND_BUILD_TIME
  value: "False"
- key: SENTRY_DSN
  scope: RUN_AND_BUILD_TIME
  value: https://<EMAIL>/4505356310675456
- key: SENTRY_ENV
  scope: RUN_AND_BUILD_TIME
  value: staging
- key: DJANGO_ENV
  scope: RUN_AND_BUILD_TIME
  value: dev
- key: DJANGO_SETTINGS_MODULE
  scope: RUN_AND_BUILD_TIME
  value: config.settings
- key: LANGUAGE_CODE
  scope: RUN_AND_BUILD_TIME
  value: en-US
- key: TIME_ZONE
  scope: RUN_AND_BUILD_TIME
  value: Europe/Lisbon
- key: STAFF
  scope: RUN_AND_BUILD_TIME
  value: '{"Pedro": {"first_name": "Pedro", "last_name": "Gaudêncio"}}'
- key: ADMIN_PASSWORD
  scope: RUN_AND_BUILD_TIME
  value: Pass@123
- key: TEACHER_PASSWORD
  scope: RUN_AND_BUILD_TIME
  value: Pass@123
- key: MASTER_PASSWORD_ENABLED
  scope: RUN_AND_BUILD_TIME
  value: "True"
- key: MASTER_PASSWORD
  scope: RUN_AND_BUILD_TIME
  value: thisisaverybadidea!WDlKEIRucdu74NlKfiseC05
- key: CACHING_ENABLED
  scope: RUN_AND_BUILD_TIME
  value: "True"
- key: USE_REDIS_CHANNELS
  scope: RUN_AND_BUILD_TIME
  value: "True"
- key: REDIS_CACHE_URL
  scope: RUN_AND_BUILD_TIME
  value: redis://default:<EMAIL>:25061
- key: REDIS_CHANNELS_URL
  scope: RUN_AND_BUILD_TIME
  value: redis://default:<EMAIL>:25061
- key: DEFAULT_CACHE_TTL
  scope: RUN_AND_BUILD_TIME
  value: "1"
- key: MINIMUM_CACHE_TTL
  scope: RUN_AND_BUILD_TIME
  value: "1"
- key: MAXIMUM_CACHE_TTL
  scope: RUN_AND_BUILD_TIME
  value: "2"
- key: CELERY_BROKER
  scope: RUN_AND_BUILD_TIME
  value: redis://redis:6379/0
- key: CELERY_BACKEND
  scope: RUN_AND_BUILD_TIME
  value: redis://redis:6379/0
- key: BASE_FRONTEND_URL
  scope: RUN_AND_BUILD_TIME
  value: https://dev.advaya.life/
- key: CMS_URL
  scope: RUN_AND_BUILD_TIME
  value: https://cms.dev.advaya.life/
- key: CMS_MAGIC_KEY
  scope: RUN_AND_BUILD_TIME
  value: testMagicKey
- key: DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN
  scope: RUN_AND_BUILD_TIME
  value: ${DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN}
- key: MEILISEARCH_URL
  scope: RUN_AND_BUILD_TIME
  value: https://search.dev.advaya.life
- key: MEILISEARCH_API_TOKEN
  scope: RUN_AND_BUILD_TIME
  value: 66a1a3d2ab39c7d326f7abc773ea14f0
- key: CHARGEBEE_API_KEY
  scope: RUN_AND_BUILD_TIME
  value: test_aBt8EdKJ1rL8LnCf9v1aitMEJgOHSD1P
- key: CHARGEBEE_SITE
  scope: RUN_AND_BUILD_TIME
  value: advaya-test
- key: CHARGEBEE_MAGIC_KEY
  scope: RUN_AND_BUILD_TIME
  value: testMagicKey
- key: CHARGEBEE_FREE_PLAN_ID
  scope: RUN_AND_BUILD_TIME
  value: free
- key: CHARGEBEE_COMMUNITY_MONTHLY_PLAN_ID
  scope: RUN_AND_BUILD_TIME
  value: community-GBP-Monthly
- key: CHARGEBEE_COMMUNITY_YEARLY_PLAN_ID
  scope: RUN_AND_BUILD_TIME
  value: community-GBP-Yearly
- key: CHARGEBEE_THRIVE_MONTHLY_PLAN_ID
  scope: RUN_AND_BUILD_TIME
  value: thrive-GBP-Monthly
- key: CHARGEBEE_THRIVE_YEARLY_PLAN_ID
  scope: RUN_AND_BUILD_TIME
  value: thrive-GBP-Yearly
- key: CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS
  scope: RUN_AND_BUILD_TIME
  value: '["community-GBP-Monthly"]'
- key: CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS
  scope: RUN_AND_BUILD_TIME
  value: '["community-GBP-Yearly"]'
- key: CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS
  scope: RUN_AND_BUILD_TIME
  value: '["thrive-GBP-Monthly", "thrive-advaya-GBP-Monthly", "thrive-2024-GBP-monthly",
    "thrive-advaya-GBP-quarterly"]'
- key: CHARGEBEE_THRIVE_YEARLY_PLAN_IDS
  scope: RUN_AND_BUILD_TIME
  value: '["thrive-GBP-Yearly", "thrive-advaya-GBP-Yearly", "thrive-2024-GBP-yearly"]'
- key: CHARGEBEE_COMMUNITY_COURSES_DISCOUNT_COUPON_ID
  scope: RUN_AND_BUILD_TIME
  value: r0ibDYGalHYrXqqYh4UChN0MMqGHLDM31Zh9
- key: CHARGEBEE_SUBSCRIPTION_GIFT_ID
  scope: RUN_AND_BUILD_TIME
  value: Gift-Thrive-Membership-GBP
- key: CHARGEBEE_GIFT_COUPON_PREFIX
  scope: RUN_AND_BUILD_TIME
  value: GIFT-
- key: CHARGEBEE_ABANDONED_CART_DISCOUNT_PERCENTAGE
  scope: RUN_AND_BUILD_TIME
  value: "15.0"
- key: CHARGEBEE_ABANDONED_CART_DISCOUNT_VALID_DAYS
  scope: RUN_AND_BUILD_TIME
  value: "7"
- key: POSTGRES_NAME
  scope: RUN_AND_BUILD_TIME
  value: defaultdb
- key: POSTGRES_USER
  scope: RUN_AND_BUILD_TIME
  value: doadmin
- key: POSTGRES_PASSWORD
  scope: RUN_AND_BUILD_TIME
  value: AVNS_cNaTW7H7vXUhgYCquYB
- key: POSTGRES_HOST
  scope: RUN_AND_BUILD_TIME
  value: advaya-api-dev-db-do-user-11248473-0.b.db.ondigitalocean.com
- key: POSTGRES_PORT
  scope: RUN_AND_BUILD_TIME
  value: "25060"
- key: DB_IGNORE_SSL
  scope: RUN_AND_BUILD_TIME
  value: "False"
- key: MARKETING_TRIGGERS_ENABLED
  scope: RUN_AND_BUILD_TIME
  value: "True"
- key: USE_DIGITAL_OCEAN_SPACES
  scope: RUN_AND_BUILD_TIME
  value: "True"
- key: DIGITAL_OCEAN_ACCESS_KEY
  scope: RUN_AND_BUILD_TIME
  value: DO00VLJYJU6T7PM33TL8
- key: DIGITAL_OCEAN_SECRET_KEY
  scope: RUN_AND_BUILD_TIME
  value: xkiias7RlpgEJ3iPyZkYkTGGhQQRmujLX9Ujt2gxCJI
- key: DIGITAL_OCEAN_REGION
  scope: RUN_AND_BUILD_TIME
  value: ams3
- key: DIGITAL_OCEAN_BUCKET_NAME
  scope: RUN_AND_BUILD_TIME
  value: advaya-dev
- key: HUBSPOT_ACCESS_TOKEN
  scope: RUN_AND_BUILD_TIME
  value: ********************************************
- key: HUBSPOT_NEWSLETTER_GENERAL_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "161"
- key: HUBSPOT_NEWSLETTER_GLOBAL_USERS_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "137"
- key: HUBSPOT_FREE_MEMBERS_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "138"
- key: HUBSPOT_COMMUNITY_MONTHLY_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "139"
- key: HUBSPOT_COMMUNITY_YEARLY_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "139"
- key: HUBSPOT_THRIVE_MONTHLY_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "140"
- key: HUBSPOT_THRIVE_YEARLY_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "140"
- key: HUBSPOT_GIFT_GIVERS_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "948"
- key: HUBSPOT_GIFT_RECEIVERS_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "949"
- key: HUBSPOT_INTERESTS_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "989"
- key: HUBSPOT_ABANDONED_CART_YEARLY_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "1497"
- key: HUBSPOT_ABANDONED_CART_YEARLY_COUPON_CODE
  scope: RUN_AND_BUILD_TIME
  value: COME-BACK-15
- key: HUBSPOT_ABANDONED_CART_QUARTERLY_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "1498"
- key: HUBSPOT_ABANDONED_CART_QUARTERLY_COUPON_CODE
  scope: RUN_AND_BUILD_TIME
  value: COME-BACK-10
- key: HUBSPOT_ABANDONED_CART_PURCHASE_LIST_ID
  scope: RUN_AND_BUILD_TIME
  value: "1499"
- key: MUX_ACCESS_TOKEN_ID
  scope: RUN_AND_BUILD_TIME
  value: 01b50cd7-f05b-4059-97a6-ab5bb82b45f4
- key: MUX_SECRET_KEY
  scope: RUN_AND_BUILD_TIME
  value: M44atyC1VdLwOsCt0kybmHUYpIIVQRIt0VQZUJJwVZz0kB5g2TSp4gAGC1Xjb35A62za5I/rNWd
- key: NODEBB_ENABLED
  scope: RUN_AND_BUILD_TIME
  value: "0"
- key: NODEBB_API_TOKEN
  scope: RUN_AND_BUILD_TIME
  value: 1b1668fb-7d0e-486a-87a2-21d463388227
- key: NODEBB_JWT_SECRET
  scope: RUN_AND_BUILD_TIME
  value: W4f8EYbfuo7tVksnJ83t4sRZJ1fBAdCUpCdc8mAQCmnX6IX33qeQf61iYoZHfLBo
- key: NODEBB_API_UID
  scope: RUN_AND_BUILD_TIME
  value: "1"
- key: NODEBB_COMMUNITY_URL
  scope: RUN_AND_BUILD_TIME
  value: https://community-dev.advaya.life
- key: NODEBB_COMMUNITY_INITIAL_GROUP_NAMES
  scope: RUN_AND_BUILD_TIME
  value: '["Community", "Welcome & Connecting", "Creative Practice", "Ecology", "Consciousness",
    "Culture", "Feedback & Announcements"]'
- key: AVATAR_GENERATOR_URL
  scope: RUN_AND_BUILD_TIME
  value: https://ui-avatars.com/api/?name=
- key: ZOOM_CLIENT_ID
  scope: RUN_AND_BUILD_TIME
  value: y8DbEbs3RGCG2bYUOYWyEw
- key: ZOOM_CLIENT_SECRET
  scope: RUN_AND_BUILD_TIME
  value: vkXFb6ESv1cUznlG6aDCQyuI9E0Oi8s2
- key: ZOOM_SECRET_TOKEN
  scope: RUN_AND_BUILD_TIME
  value: inEclA1KTSGDok_TjIFbmw
- key: ZOOM_VERIFICATION_TOKEN
  scope: RUN_AND_BUILD_TIME
  value: 12pK86tdRm6Se2D3NkhABw
- key: DIGITAL_OCEAN_FUNCTIONS_NAMESPACE
  scope: RUN_AND_BUILD_TIME
  value: advaya
- key: DIGITAL_OCEAN_FUNCTIONS_REGION
  scope: RUN_AND_BUILD_TIME
  value: lon
ingress:
  rules:
  - component:
      name: advaya-api
    match:
      path:
        prefix: /
name: advaya-api-dev-v2
region: lon
services:
- envs:
  - key: DATABASE_URL
    scope: RUN_TIME
    value: ${advaya-api-dev-db.DATABASE_URL}
  http_port: 8080
  image:
    deploy_on_push:
      enabled: true
    registry_type: DOCR
    repository: advaya-api
    tag: dev
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb
  log_destinations:
  - logtail:
      token: 1EXivErebwynyvAFGveC8gys
    name: advaya-api-dev
  name: advaya-api
